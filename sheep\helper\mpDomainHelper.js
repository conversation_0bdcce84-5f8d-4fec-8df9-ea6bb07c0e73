/**
 * 微信小程序域名检查工具
 */

// 常见的可能有问题的域名模式
const PROBLEMATIC_DOMAINS = [
  'tmp',
  'localhost',
  '127.0.0.1',
  'undefined',
  'null'
];

/**
 * 检查URL是否可能在微信小程序中被拒绝
 * @param {string} url 要检查的URL
 * @returns {boolean} true表示可能有问题
 */
export function isProblematicUrl(url) {
  if (!url || typeof url !== 'string') {
    return true;
  }
  
  // 检查是否包含问题域名
  for (const domain of PROBLEMATIC_DOMAINS) {
    if (url.includes(domain)) {
      console.warn('检测到可能有问题的域名:', domain, '在URL中:', url);
      return true;
    }
  }
  
  // 检查是否是有效的HTTP/HTTPS URL
  if (url.startsWith('http')) {
    try {
      const urlObj = new URL(url);
      // 检查域名是否看起来有效
      if (!urlObj.hostname || urlObj.hostname.length < 3) {
        console.warn('域名看起来无效:', urlObj.hostname);
        return true;
      }
      return false;
    } catch (error) {
      console.warn('URL格式无效:', url, error);
      return true;
    }
  }
  
  // 相对路径通常是安全的
  return false;
}

/**
 * 获取安全的图片URL，专门为微信小程序优化
 * @param {string} url 原始URL
 * @param {string} fallbackUrl 备选URL
 * @returns {string} 安全的URL
 */
export function getSafeUrlForMP(url, fallbackUrl = '') {
  // 如果原始URL有问题，使用备选URL
  if (isProblematicUrl(url)) {
    console.log('原始URL有问题，使用备选URL:', url, '->', fallbackUrl);
    return fallbackUrl || '/assets/addons/shopro/uniapp/empty_network.png';
  }
  
  // 确保使用HTTPS
  if (url.startsWith('http:')) {
    url = url.replace('http:', 'https:');
    console.log('转换为HTTPS:', url);
  }
  
  return url;
}

/**
 * 检查当前环境的网络配置
 */
export function checkNetworkConfig() {
  // #ifdef MP-WEIXIN
  console.log('=== 微信小程序网络配置检查 ===');
  console.log('当前环境: 微信小程序');

  // 获取当前配置的CDN域名
  try {
    const appStore = uni.getStorageSync('app-store');
    const cdnUrl = appStore?.info?.cdnurl;

    if (cdnUrl && cdnUrl !== 'undefined' && cdnUrl !== 'null') {
      console.log('✓ CDN域名已配置:', cdnUrl);
      console.log('请确保此域名已添加到微信小程序后台的以下配置中:');
      console.log('  1. request合法域名');
      console.log('  2. downloadFile合法域名');
      console.log('  3. uploadFile合法域名');
    } else {
      console.warn('✗ CDN域名未配置或无效:', cdnUrl);
      console.log('这可能导致图片加载失败');
    }

    // 检查API域名
    const baseUrl = appStore?.info?.baseUrl;
    if (baseUrl) {
      console.log('✓ API域名:', baseUrl);
    }

  } catch (error) {
    console.error('获取配置信息失败:', error);
  }

  console.log('');
  console.log('如果遇到域名相关错误，请:');
  console.log('1. 登录微信小程序后台 (mp.weixin.qq.com)');
  console.log('2. 进入 开发 -> 开发设置 -> 服务器域名');
  console.log('3. 将相关域名添加到对应的合法域名列表中');
  console.log('================================');
  // #endif
}
