/**
 * Shopro + qs-canvas 绘制海报
 * @version 1.0.0
 * <AUTHOR>
 * @param {Object} options - 海报参数
 * @param {Object} vm - 自定义组件实例
 */

import sheep from '@/sheep';
import QSCanvas from 'qs-canvas';
import { getPosterData } from './poster';

export default async function useCanvas(options, vm) {
  const width = options.width;
  const qsc = new QSCanvas(
    {
      canvasId: options.canvasId,
      width: options.width,
      height: options.height,
      setCanvasWH: (canvas) => {
        options.height = canvas.height;
      },
    },
    vm,
  );

  // 支持异步获取海报数据
  let drawer;
  try {
    drawer = await getPosterData(options);
  } catch (error) {
    console.error('获取海报数据失败:', error);
    throw new Error('海报数据获取失败，请稍后重试');
  }

  // 绘制背景图
  let background;
  try {
    console.log('开始绘制背景图:', drawer.background);
    background = await qsc.drawImg({
      type: 'image',
      val: drawer.background,
      x: 0,
      y: 0,
      width,
      mode: 'widthFix',
      zIndex: 0,
    });
    console.log('背景图绘制成功');
  } catch (error) {
    console.error('背景图绘制失败:', error);
    console.error('背景图URL:', drawer.background);

    // 尝试使用纯色背景作为备选方案
    try {
      console.log('尝试使用纯色背景...');
      // 绘制一个简单的渐变背景
      const ctx = qsc.ctx;
      const gradient = ctx.createLinearGradient(0, 0, 0, options.height || 600);
      gradient.addColorStop(0, '#f8f9fa');
      gradient.addColorStop(1, '#e9ecef');

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, options.height || 600);

      background = {
        width: width,
        bottom: options.height || 600
      };
      console.log('纯色背景绘制成功');
    } catch (fallbackError) {
      console.error('纯色背景绘制也失败:', fallbackError);
      throw new Error(`背景图加载失败: ${error.message || '请检查网络连接'}`);
    }
  }
  await qsc.updateCanvasWH({
    width: background.width,
    height: background.bottom,
  });

  let list = drawer.list;

  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    // 绘制文字
    if (item.type === 'text') {
      await qsc.drawText(item);
    }
    // 绘制图片
    if (item.type === 'image') {
      try {
        if (item.d) {
          qsc.setCircle({
            x: item.x,
            y: item.y,
            d: item.d,
            clip: true,
          });
        }

        if (item.r) {
          qsc.setRect({
            x: item.x,
            y: item.y,
            height: item.height,
            width: item.width,
            r: item.r,
            clip: true,
          });
        }
        await qsc.drawImg(item);
        qsc.restore();
      } catch (error) {
        console.error(`图片绘制失败 (${item.name}):`, error);
        qsc.restore(); // 确保恢复画布状态
        // 继续执行，不中断整个海报生成过程
      }
    }

    // 绘制二维码
    if (item.type === 'qrcode') {
      await qsc.drawQrCode(item);
    }
  }

  await qsc.draw();
  // 延迟执行, 防止不稳定
  setTimeout(async () => {
    try {
      console.log('开始生成海报图片...');
      options.src = await qsc.toImage();
      console.log('海报图片生成成功:', options.src);
    } catch (error) {
      console.error('海报图片生成失败:', error);
      options.error = `图片生成失败: ${error.message || '未知错误'}`;
    }
  }, 100);
  return options;
}
