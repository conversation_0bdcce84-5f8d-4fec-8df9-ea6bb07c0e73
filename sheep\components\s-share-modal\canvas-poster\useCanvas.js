/**
 * Shopro + qs-canvas 绘制海报
 * @version 1.0.0
 * <AUTHOR>
 * @param {Object} options - 海报参数
 * @param {Object} vm - 自定义组件实例
 */

import sheep from '@/sheep';
import QSCanvas from 'qs-canvas';
import { getPosterData } from './poster';

export default async function useCanvas(options, vm) {
  const width = options.width;
  const qsc = new QSCanvas(
    {
      canvasId: options.canvasId,
      width: options.width,
      height: options.height,
      setCanvasWH: (canvas) => {
        options.height = canvas.height;
      },
    },
    vm,
  );

  // 支持异步获取海报数据
  let drawer;
  try {
    drawer = await getPosterData(options);
  } catch (error) {
    console.error('获取海报数据失败:', error);
    throw new Error('海报数据获取失败，请稍后重试');
  }

  // 绘制背景图
  let background;
  try {
    background = await qsc.drawImg({
      type: 'image',
      val: drawer.background,
      x: 0,
      y: 0,
      width,
      mode: 'widthFix',
      zIndex: 0,
    });
  } catch (error) {
    console.error('背景图绘制失败:', error);
    throw new Error('背景图加载失败，请检查网络连接');
  }
  await qsc.updateCanvasWH({
    width: background.width,
    height: background.bottom,
  });

  let list = drawer.list;

  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    // 绘制文字
    if (item.type === 'text') {
      await qsc.drawText(item);
    }
    // 绘制图片
    if (item.type === 'image') {
      try {
        if (item.d) {
          qsc.setCircle({
            x: item.x,
            y: item.y,
            d: item.d,
            clip: true,
          });
        }

        if (item.r) {
          qsc.setRect({
            x: item.x,
            y: item.y,
            height: item.height,
            width: item.width,
            r: item.r,
            clip: true,
          });
        }
        await qsc.drawImg(item);
        qsc.restore();
      } catch (error) {
        console.error(`图片绘制失败 (${item.name}):`, error);
        qsc.restore(); // 确保恢复画布状态
        // 继续执行，不中断整个海报生成过程
      }
    }

    // 绘制二维码
    if (item.type === 'qrcode') {
      await qsc.drawQrCode(item);
    }
  }

  await qsc.draw();
  // 延迟执行, 防止不稳定
  setTimeout(async () => {
    options.src = await qsc.toImage();
  }, 100);
  return options;
}
