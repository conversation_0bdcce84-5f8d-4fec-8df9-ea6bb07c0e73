/**
 * Shopro + qs-canvas 绘制海报
 * @version 1.0.0
 * <AUTHOR>
 * @param {Object} options - 海报参数
 * @param {Object} vm - 自定义组件实例
 */

import sheep from '@/sheep';
import QSCanvas from 'qs-canvas';
import { getPosterData } from './poster';
import { isProblematicUrl } from '@/sheep/helper/mpDomainHelper';

// 生成简单海报的备选方案
async function generateSimplePoster(qsc, options) {
  try {
    console.log('开始生成简单海报');

    const ctx = qsc.ctx;
    const width = options.width;
    const height = options.height || 600;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制背景
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, '#f8f9fa');
    gradient.addColorStop(1, '#e9ecef');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // 绘制标题
    ctx.fillStyle = '#333';
    ctx.font = 'bold 24px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('邀请海报', width / 2, 80);

    // 绘制用户信息
    try {
      const userInfo = sheep.$store('user').userInfo;
      if (userInfo && userInfo.nickname) {
        ctx.font = '18px sans-serif';
        ctx.fillText(`邀请人: ${userInfo.nickname}`, width / 2, 150);
      }
    } catch (error) {
      console.warn('获取用户信息失败:', error);
    }

    // 绘制邀请文案
    ctx.font = '16px sans-serif';
    ctx.fillText('扫码加入我们', width / 2, 200);

    // 绘制底部信息
    ctx.font = '14px sans-serif';
    ctx.fillStyle = '#666';
    ctx.fillText('长按保存图片', width / 2, height - 50);

    await qsc.draw();

    // 等待一段时间确保绘制完成
    await new Promise(resolve => setTimeout(resolve, 500));

    return await qsc.toImage();
  } catch (error) {
    console.error('简单海报生成失败:', error);
    return null;
  }
}

export default async function useCanvas(options, vm) {
  console.log('=== 开始海报生成 ===');
  console.log('Canvas选项:', options);

  const width = options.width;

  let qsc;
  try {
    qsc = new QSCanvas(
      {
        canvasId: options.canvasId,
        width: options.width,
        height: options.height,
        setCanvasWH: (canvas) => {
          options.height = canvas.height;
        },
      },
      vm,
    );
    console.log('QSCanvas 初始化成功');
  } catch (error) {
    console.error('QSCanvas 初始化失败:', error);
    // 如果连QSCanvas都初始化失败，直接返回错误
    options.error = 'Canvas初始化失败';
    return options;
  }

  // 支持异步获取海报数据
  let drawer;
  try {
    console.log('开始获取海报数据...');
    drawer = await getPosterData(options);
    console.log('海报数据获取成功:', drawer);
  } catch (error) {
    console.error('获取海报数据失败，使用简单海报:', error);
    // 如果获取海报数据失败，直接生成简单海报
    try {
      const simplePosterSrc = await generateSimplePoster(qsc, options);
      if (simplePosterSrc) {
        options.src = simplePosterSrc;
        options.error = null;
        return options;
      }
    } catch (simpleError) {
      console.error('简单海报生成也失败:', simpleError);
    }
    throw new Error('海报数据获取失败，请稍后重试');
  }

  // 绘制背景图
  let background;

  // 检查背景图URL是否安全
  console.log('开始绘制背景图:', drawer.background);

  // 在微信小程序中，如果背景图URL有问题，直接使用纯色背景
  let shouldUseFallback = false;
  try {
    shouldUseFallback = isProblematicUrl(drawer.background);
  } catch (error) {
    console.error('检查URL时出错:', error);
    shouldUseFallback = false; // 如果检查失败，尝试正常流程
  }

  if (shouldUseFallback) {
    console.log('背景图URL有问题，直接使用纯色背景');
    try {
      // 绘制一个简单的渐变背景
      const ctx = qsc.ctx;
      const gradient = ctx.createLinearGradient(0, 0, 0, options.height || 600);
      gradient.addColorStop(0, '#f8f9fa');
      gradient.addColorStop(1, '#e9ecef');

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, options.height || 600);

      background = {
        width: width,
        bottom: options.height || 600
      };
      console.log('纯色背景绘制成功');
    } catch (fallbackError) {
      console.error('纯色背景绘制失败:', fallbackError);
      throw new Error('背景绘制失败');
    }
  } else {
    // 尝试绘制图片背景
    try {
      background = await qsc.drawImg({
        type: 'image',
        val: drawer.background,
        x: 0,
        y: 0,
        width,
        mode: 'widthFix',
        zIndex: 0,
      });
      console.log('背景图绘制成功');
    } catch (error) {
      console.error('背景图绘制失败:', error);
      console.error('背景图URL:', drawer.background);

      // 尝试使用纯色背景作为备选方案
      try {
        console.log('尝试使用纯色背景...');
        const ctx = qsc.ctx;
        const gradient = ctx.createLinearGradient(0, 0, 0, options.height || 600);
        gradient.addColorStop(0, '#f8f9fa');
        gradient.addColorStop(1, '#e9ecef');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, options.height || 600);

        background = {
          width: width,
          bottom: options.height || 600
        };
        console.log('纯色背景绘制成功');
      } catch (fallbackError) {
        console.error('纯色背景绘制也失败:', fallbackError);
        throw new Error(`背景图加载失败: ${error.message || '请检查网络连接'}`);
      }
    }
  }
  await qsc.updateCanvasWH({
    width: background.width,
    height: background.bottom,
  });

  let list = drawer.list;

  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    // 绘制文字
    if (item.type === 'text') {
      await qsc.drawText(item);
    }
    // 绘制图片
    if (item.type === 'image') {
      // 检查图片URL是否安全
      let isImageSafe = true;
      try {
        isImageSafe = !isProblematicUrl(item.val);
      } catch (error) {
        console.error('检查图片URL时出错:', error);
        isImageSafe = true; // 如果检查失败，尝试正常绘制
      }

      if (!isImageSafe) {
        console.warn(`跳过有问题的图片 (${item.name}):`, item.val);
        continue; // 跳过这个图片，继续处理下一个元素
      }

      try {
        if (item.d) {
          qsc.setCircle({
            x: item.x,
            y: item.y,
            d: item.d,
            clip: true,
          });
        }

        if (item.r) {
          qsc.setRect({
            x: item.x,
            y: item.y,
            height: item.height,
            width: item.width,
            r: item.r,
            clip: true,
          });
        }

        console.log(`绘制图片 (${item.name}):`, item.val);
        await qsc.drawImg(item);
        qsc.restore();
        console.log(`图片绘制成功 (${item.name})`);
      } catch (error) {
        console.error(`图片绘制失败 (${item.name}):`, error);
        qsc.restore(); // 确保恢复画布状态
        // 继续执行，不中断整个海报生成过程
      }
    }

    // 绘制二维码
    if (item.type === 'qrcode') {
      await qsc.drawQrCode(item);
    }
  }

  await qsc.draw();

  // 使用 Promise 包装 toImage 过程，增加重试机制
  return new Promise((resolve) => {
    let retryCount = 0;
    const maxRetries = 3;

    const tryGenerateImage = async () => {
      try {
        console.log(`开始生成海报图片... (尝试 ${retryCount + 1}/${maxRetries})`);

        // 增加延迟，确保 canvas 绘制完成
        await new Promise(resolve => setTimeout(resolve, 200 + retryCount * 100));

        const imageSrc = await qsc.toImage();
        console.log('海报图片生成成功:', imageSrc);

        options.src = imageSrc;
        options.error = null;
        resolve(options);

      } catch (error) {
        console.error(`海报图片生成失败 (尝试 ${retryCount + 1}):`, error);
        retryCount++;

        if (retryCount < maxRetries) {
          console.log(`准备重试... (${retryCount}/${maxRetries})`);
          setTimeout(tryGenerateImage, 500 * retryCount); // 递增延迟
        } else {
          console.error('所有重试都失败了，尝试生成纯文本海报');

          // 最后的备选方案：生成简单海报
          try {
            const simplePosterSrc = await generateSimplePoster(qsc, options);
            if (simplePosterSrc) {
              options.src = simplePosterSrc;
              options.error = null;
              console.log('简单海报生成成功');
            } else {
              options.error = `图片生成失败: ${error.message || '未知错误'}`;
              options.src = '';
            }
          } catch (simpleError) {
            console.error('简单海报生成也失败:', simpleError);
            options.error = `图片生成失败: ${error.message || '未知错误'}`;
            options.src = '';
          }

          resolve(options);
        }
      }
    };

    // 开始第一次尝试
    setTimeout(tryGenerateImage, 100);
  });
}
