<!-- 页面 -->
<template>
  <su-popup :show="show" round="10" @close="onClosePoster" type="center" class="popup-box">
    <view class="ss-flex-col ss-col-center ss-row-center" style="min-height: 1000rpx;">
      <!-- 加载状态 -->
      <view
        v-if="poster.src === '' && !poster.error"
        class="poster-title ss-flex ss-row-center"
        :style="{
          height: poster.height + 'px',
          width: poster.width + 'px',
        }"
      >
        {{ poster.loading ? '海报生成中...' : '海报加载中...' }}
      </view>

      <!-- 错误状态 -->
      <view
        v-else-if="poster.error"
        class="poster-error ss-flex ss-col-center ss-row-center"
        :style="{
          height: poster.height + 'px',
          width: poster.width + 'px',
        }"
      >
        <text class="error-text">{{ poster.error }}</text>
        <button class="retry-btn ss-reset-button" @tap="retryGenerate">重新生成</button>
      </view>

      <!-- 成功状态 - 显示海报图片 -->
      <image
        v-else-if="poster.src !== ''"
        class="poster-img"
        :src="poster.src"
        :style="{
          height: poster.height + 'px',
          width: poster.width + 'px',
        }"
      ></image>
      <canvas
        class="hideCanvas"
        :canvas-id="poster.canvasId"
        :id="poster.canvasId"
        :style="{
          height: poster.height + 'px',
          width: poster.width + 'px',
        }"
      />
      <!-- 操作按钮 - 只在海报生成成功时显示 -->
      <view
        class="poster-btn-box ss-m-t-20 ss-flex ss-row-between ss-col-center"
        v-if="poster.src !== '' && !poster.error"
      >
        <button class="cancel-btn ss-reset-button" @tap="onClosePoster">取消</button>
        <button class="save-btn ss-reset-button ui-BG-Main" @tap="onSavePoster">
          {{
            ['wechatOfficialAccount', 'H5'].includes(sheep.$platform.name)
              ? '长按图片保存'
              : '保存图片'
          }}
        </button>
      </view>
    </view>
  </su-popup>
</template>

<script setup>
  import { reactive, getCurrentInstance } from 'vue';
  import sheep from '@/sheep';
  import useCanvas from './useCanvas';

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
    shareInfo: {
      type: Object,
      default() {},
    },
  });

  const poster = reactive({
    canvasId: 'canvasId-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11),
    width: sheep.$platform.device.windowWidth * 0.9,
    height: 600,
    src: '',
    loading: false,
    error: null,
  });

  const emits = defineEmits(['success', 'close']);
  const vm = getCurrentInstance();
  const onClosePoster = () => {
    emits('close');
  };
  const onSavePoster = () => {
    if (['WechatOfficialAccount', 'H5'].includes(sheep.$platform.name)) {
      sheep.$helper.toast('请长按图片保存');
      return;
    }

    uni.saveImageToPhotosAlbum({
      filePath: poster.src,
      success: () => {
        onClosePoster();
        sheep.$helper.toast('保存成功');
      },
      fail: (err) => {
        sheep.$helper.toast('保存失败');
        console.log('图片保存失败:', err);
      },
    });
  };

  async function getPoster() {
    try {
      poster.src = '';
      poster.loading = true;
      poster.error = null;

      poster.shareInfo = props.shareInfo;
      // #ifdef APP-PLUS
      poster.canvasId = 'canvasId-' + new Date().getTime();
      // #endif

      console.log('开始生成海报，shareInfo:', poster.shareInfo);
      const canvas = await useCanvas(poster, vm);

      // 检查生成结果
      if (canvas.error) {
        poster.error = canvas.error;
        poster.loading = false;
        sheep.$helper.toast(canvas.error);
        throw new Error(canvas.error);
      } else if (canvas.src) {
        poster.src = canvas.src;
        poster.loading = false;
        console.log('海报生成完成');
        return canvas;
      } else {
        // 如果没有错误但也没有图片，可能还在处理中
        poster.loading = false;
        poster.error = '海报生成超时，请重试';
        sheep.$helper.toast('海报生成超时，请重试');
        throw new Error('海报生成超时');
      }
    } catch (error) {
      poster.loading = false;
      poster.error = error.message || '海报生成失败';
      console.error('海报生成失败:', error);
      sheep.$helper.toast(poster.error);
      throw error;
    }
  }

  // 重试生成海报
  const retryGenerate = async () => {
    try {
      await getPoster();
    } catch (error) {
      // 错误已在 getPoster 中处理
    }
  };

  defineExpose({
    getPoster,
  });
</script>

<style lang="scss" scoped>
  .popup-box {
    position: relative;
  }
  .poster-title {
    color: #999;
  }
  // 分享海报
  .poster-btn-box {
    width: 600rpx;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -80rpx;
    .cancel-btn {
      width: 240rpx;
      height: 70rpx;
      line-height: 70rpx;
      background: $white;
      border-radius: 35rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: $dark-9;
    }
    .save-btn {
      width: 240rpx;
      height: 70rpx;
      line-height: 70rpx;
      border-radius: 35rpx;
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  .poster-img {
    border-radius: 20rpx;
  }

  .poster-error {
    padding: 40rpx;
    background: #f5f5f5;
    border-radius: 10rpx;
  }

  .error-text {
    font-size: 28rpx;
    color: #ff4444;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .retry-btn {
    padding: 16rpx 32rpx;
    background: #007aff;
    color: white;
    border-radius: 8rpx;
    font-size: 28rpx;
  }

  .hideCanvas {
    position: fixed;
    top: -99999rpx;
    left: -99999rpx;
    z-index: -99999;
  }
</style>
