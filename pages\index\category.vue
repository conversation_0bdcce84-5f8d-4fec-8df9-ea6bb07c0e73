<template>
  <view style="background-color: #ffffff;">
    <view style="width: 100%;padding-top: 100rpx;position: fixed;top: 0;    background: #fff;">
      <view class="search-container">
        <view class="search-box">
          <view class="search-icon">
            <image src="/static/img/s.png" style="width: 30rpx;height: 30rpx;"></image>
          </view>
          <input class="search-input" type="text" placeholder="搜索您需要的饰品" v-model="searchKeyword" @confirm="onSearch"
            confirm-type="search" />
          <view class="search-btn" @click="onSearch">
            <text>搜索</text>
          </view>
        </view>
      </view>
    </view>
    <view style="padding-top: 80px;"></view>
    <view style="height: 1px;background-color:#EEEEEE;width: 100%;margin: 20rpx 0rpx;"></view>
    <view class="s-category">
      <view class="three-level-wrap ss-flex ss-col-top">
        <view class="side-menu-wrap" :style="[{ top: Number(statusBarHeight + 88) + 'rpx' }]">
          <scroll-view scroll-y :style="[{ height: pageHeight + 'px' }]">
            <view class="menu-item ss-flex" v-for="(item, index) in state.categoryList?.children" :key="item.id"
              :class="[{ 'menu-item-active': index == state.activeMenu }]" @tap="onMenu(index)">
              <!-- ss-line-1 -->
              <view class="menu-title">
                {{ item.name }}
              </view>
              <view v-if="index == state.activeMenu"
                style="height: 30rpx;width: 7rpx;background-color: #7C332C;position: absolute;left: 0;"></view>
            </view>
          </scroll-view>
        </view>
        <view class="goods-list-box" v-if="state.categoryList?.children?.length">
          <image v-if="state.categoryList.children[state.activeMenu].image" class="banner-img"
            :src="sheep.$url.cdn(state.categoryList.children[state.activeMenu].image)" mode="widthFix">
          </image>
          <first-one v-if="state.categoryList.style === 'first_one'" :data="state.categoryList"
            :activeMenu="state.activeMenu" :pagination="state.pagination" />
          <first-two v-if="state.categoryList.style === 'first_two'" :data="state.categoryList"
            :activeMenu="state.activeMenu" :pagination="state.pagination" />
          <second-one v-if="state.categoryList.style === 'second_one'" :data="state.categoryList"
            :activeMenu="state.activeMenu" :pagination="state.pagination" />
          <third-one v-if="state.categoryList.style === 'third_one'" :data="state.categoryList"
            :activeMenu="state.activeMenu" :pagination="state.pagination" />
          <uni-load-more v-if="
            (state.categoryList.style === 'first_one' ||
              state.categoryList.style === 'first_two') &&
            state.pagination.total > 0
          " :status="state.loadStatus" :content-text="{
            contentdown: '点击查看更多',
          }" @tap="loadmore" />
        </view>
      </view>
    </view>

    <!-- 固定显示 -->
    <!-- <view class="car-show" v-show="!showLine" @click="getCarList">
      <image class="car-image" src="@/static/imgs/goods/gou.png"></image>
      <image class="logo-image" src="@/static/imgs/goods/gougou.png" mode=""></image>
      <image class="gou-image" src="@/static/imgs/goods/mai.png" mode=""></image>
      <image @click="toPage('/pages/index/cart')" class="cart-image" src="https://jiangxiaoxian.0rui.cn/car.png"
        mode=""></image>
    </view> -->
    <s-tabbar path="/pages/index/category" />
  </view>
</template>

<script setup>
import secondOne from './components/second-one.vue';
import thirdOne from './components/third-one.vue';
import firstOne from './components/first-one.vue';
import firstTwo from './components/first-two.vue';
import sheep from '@/sheep';

import { onLoad, onReachBottom } from '@dcloudio/uni-app';
import { computed, reactive, ref } from 'vue';
import _ from 'lodash';
const state = reactive({
  categoryList: [],
  activeMenu: '0',

  pagination: {
    data: [],
    current_page: 1,
    total: 1,
    last_page: 1,
  },
  loadStatus: '',
});

// 搜索相关数据
const searchKeyword = ref('');

const { screenHeight, safeAreaInsets, screenWidth, safeArea } = sheep.$platform.device;
const pageHeight = computed(() => safeArea.height - 44 - 50);
const statusBarHeight = sheep.$platform.device.statusBarHeight * 2;

async function getList(options) {
  const { code, data } = await sheep.$api.category.list({
    id: options.id,
  });
  if (code === 1) {
    state.categoryList = data;
  }
}

const toPage = (e) => {
  uni.navigateTo({
    url: e,
  })
}

// 搜索功能
const onSearch = () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none'
    });
    return;
  }

  // 跳转到搜索页面
  sheep.$router.go('/pages/index/search', {
    keyword: searchKeyword.value.trim()
  });
}

const onMenu = (val) => {
  state.activeMenu = val;
  if (state.categoryList.style === 'first_one' || state.categoryList.style === 'first_two') {
    state.pagination = {
      data: [],
      current_page: 1,
      total: 1,
      last_page: 1,
    };
    getGoodsList(state.categoryList.children[val].id);
  }
};

async function getGoodsList(id, page = 1, list_rows = 6) {
  state.loadStatus = 'loading';
  const res = await sheep.$api.goods.list({
    category_id: id,
    list_rows,
    page,
  });
  if (res.code === 1) {
    let couponList = _.concat(state.pagination.data, res.data.data);
    state.pagination = {
      ...res.data,
      data: couponList,
    };
    if (state.pagination.current_page < state.pagination.last_page) {
      state.loadStatus = 'more';
    } else {
      state.loadStatus = 'noMore';
    }
  }
}
// 加载更多
function loadmore() {
  if (
    state.loadStatus !== 'noMore' &&
    (state.categoryList.style === 'first_one' || state.categoryList.style === 'first_two')
  ) {
    getGoodsList(
      state.categoryList.children[state.activeMenu].id,
      state.pagination.current_page + 1,
    );
  }
}
onLoad(async (options) => {
  await getList(options);
  if (state.categoryList.style === 'first_one' || state.categoryList.style === 'first_two') {
    getGoodsList(state.categoryList.children[0].id);
  }
});
onReachBottom(() => {
  loadmore();
});
</script>

<style lang="scss" scoped>
// 搜索框样式
.search-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
}

.search-box {
  width: 68%;
  height: 60rpx;
  background: #F8F8F8;
  border-radius: 30rpx;
  display: flex;
  align-items: center;

}

.search-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 28rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  border: none;
  outline: none;
  background: transparent;
}

.search-input::placeholder {
  color: #999999;
}

.search-btn {
  width: 120rpx;
  height: 60rpx;
  background: #9E4242;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30rpx;

  text {
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 500;
  }
}

.search-btn:active {
  opacity: 0.8;
}

.s-category {
  :deep() {
    .side-menu-wrap {
      width: 200rpx;
      height: 100%;
      background-color: #f6f6f6;
      position: fixed;
      left: 0;

      .menu-item {
        width: 100%;
        height: 130rpx;
        position: relative;
        transition: all linear 0.2s;

        .menu-title {
          line-height: 45rpx;
          font-size: 30rpx;
          font-weight: 400;
          color: #999999;
          margin-left: 18rpx;
          position: relative;
          z-index: 0;
          overflow: hidden;
          // padding: 10rpx 0;
          padding: 10rpx;

          &::before {
            content: '';
            width: 64rpx;
            height: 12rpx;
            background: linear-gradient(90deg,
                var(--ui-BG-Main-gradient),
                var(--ui-BG-Main-light)) !important;
            position: absolute;
            left: -64rpx;
            bottom: 0;
            z-index: -1;
            transition: all linear 0.2s;
          }
        }

        &.menu-item-active {
          background-color: #fff;
          //border-radius: 20rpx 0 0 20rpx;

          &::before {
            content: '';
            position: absolute;
            right: 0;
            bottom: -20rpx;
            width: 20rpx;
            height: 20rpx;
            background: radial-gradient(circle at 0 100%, transparent 20rpx, #fff 0);
          }

          &::after {
            content: '';
            position: absolute;
            top: -20rpx;
            right: 0;
            width: 20rpx;
            height: 20rpx;
            background: radial-gradient(circle at 0% 0%, transparent 20rpx, #fff 0);
          }

          .menu-title {
            font-weight: 600;
            color: #7C332C;

            &::before {
              left: 0;
            }
          }
        }
      }
    }

    .goods-list-box {
      background-color: #fff;
      width: calc(100vw - 200rpx);
      padding: 10px;
      margin-left: 200rpx;
    }

    .banner-img {
      width: calc(100vw - 130px);
      border-radius: 5px;
      margin-bottom: 20rpx;
    }
  }
}

.car-show {
  height: 80rpx;
  position: fixed;
  bottom: 200rpx;
  right: 0;

  .car-image {
    width: 165rpx;
    height: 78rpx;
  }

  .logo-image {
    width: 67rpx;
    height: 67rpx;
    position: absolute;
    top: -50rpx;
    left: 80rpx;
  }

  .gou-image {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    top: 49%;
    left: 30rpx;
    transform: translateY(-50%);
  }

  .cart-image {
    width: 80rpx;
    height: 80rpx;
    position: absolute;
    top: 45%;
    right: 20rpx;
    transform: translateY(-50%);
  }
}
</style>
