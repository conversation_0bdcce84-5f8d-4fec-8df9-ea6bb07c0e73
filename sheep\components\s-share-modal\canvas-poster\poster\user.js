import sheep from '@/sheep';
import { formatImageUrlProtocol } from './index';
import { getSafeImageUrl } from '@/sheep/helper/imageValidator';
import { getSafeUrlForMP, checkNetworkConfig } from '@/sheep/helper/mpDomainHelper';

const user = async (poster) => {
  const width = poster.width;
  const userInfo = sheep.$store('user').userInfo;

  console.log('用户海报生成开始');
  console.log('海报宽度:', width);
  console.log('用户信息:', userInfo);
  console.log('海报配置:', sheep.$store('app').platform.share.posterInfo);

  // 检查网络配置
  checkNetworkConfig();

  // 获取安全的图片URL
  let backgroundUrl = await getSafeImageUrl(
    sheep.$store('app').platform.share.posterInfo.user_bg,
    'background'
  );
  let avatarUrl = await getSafeImageUrl(userInfo.avatar, 'avatar');

  // 在微信小程序中进行额外的安全检查
  backgroundUrl = getSafeUrlForMP(backgroundUrl, '/assets/addons/shopro/uniapp/empty_network.png');
  avatarUrl = getSafeUrlForMP(avatarUrl, '/static/images/default-avatar.png');

  console.log('最终背景图URL:', backgroundUrl);
  console.log('最终头像URL:', avatarUrl);

  return {
    background: formatImageUrlProtocol(backgroundUrl),
    list: [
      {
        name: 'nickname',
        type: 'text',
        val: userInfo.nickname,
        x: width / 2,
        y: width * 0.4,
        paintbrushProps: {
          textAlign: 'center',
          fillStyle: '#333',
          font: {
            fontSize: 14,
            fontFamily: 'sans-serif',
          },
        },
      },
      {
        name: 'avatar',
        type: 'image',
        val: formatImageUrlProtocol(avatarUrl),
        x: width * 0.4,
        y: width * 0.16,
        width: width * 0.2,
        height: width * 0.2,
        d: width * 0.2,
      },
      // #ifndef MP-WEIXIN
      {
        name: 'qrcode',
        type: 'qrcode',
        val: poster.shareInfo.link,
        x: width * 0.35,
        y: width * 0.84,
        size: width * 0.3,
      },
      // #endif
      // #ifdef MP-WEIXIN
      {
        name: 'wxacode',
        type: 'image',
        val: sheep.$api.third.wechat.getWxacode(poster.shareInfo.path),
        x: width * 0.35,
        y: width * 0.84,
        width: width * 0.3,
        height: width * 0.3,
      },
      // #endif
    ],
  };
};

export default user;
