/**
 * 图片验证和处理工具
 */
import sheep from '@/sheep';
import { preprocessImageForMP } from './mpImageHelper';

// 默认图片资源
const DEFAULT_IMAGES = {
  avatar: '/static/images/default-avatar.png',
  goods: '/assets/addons/shopro/uniapp/empty_network.png',
  background: '/assets/addons/shopro/uniapp/empty_network.png'
};

/**
 * 验证图片URL是否有效
 * @param {string} url 图片URL
 * @returns {Promise<boolean>} 是否有效
 */
export function validateImageUrl(url) {
  return new Promise((resolve) => {
    if (!url || typeof url !== 'string') {
      console.warn('图片URL无效:', url);
      resolve(false);
      return;
    }

    // 检查是否是有效的URL格式
    if (!url.startsWith('http') && !url.startsWith('/') && !url.startsWith('data:')) {
      console.warn('图片URL格式无效:', url);
      resolve(false);
      return;
    }

    // 使用 uni.getImageInfo 验证图片
    uni.getImageInfo({
      src: url,
      success: (res) => {
        console.log('图片验证成功:', url, res);
        resolve(true);
      },
      fail: (err) => {
        console.warn('图片验证失败:', url, err);
        resolve(false);
      }
    });
  });
}

/**
 * 获取安全的图片URL，如果原URL无效则返回默认图片
 * @param {string} url 原始图片URL
 * @param {string} type 图片类型 (avatar, goods, background)
 * @returns {Promise<string>} 安全的图片URL
 */
export async function getSafeImageUrl(url, type = 'goods') {
  try {
    console.log(`开始验证图片URL [${type}]:`, url);
    console.log(`CDN配置:`, sheep.$store('app').info.cdnurl);

    // 如果URL为空，直接返回默认图片
    if (!url) {
      const defaultUrl = sheep.$url.static(DEFAULT_IMAGES[type] || DEFAULT_IMAGES.goods);
      console.log(`URL为空，使用默认图片 [${type}]:`, defaultUrl);
      return defaultUrl;
    }

    // 处理CDN URL
    let processedUrl = sheep.$url.cdn(url);
    console.log(`处理后的URL [${type}]:`, processedUrl);

    // 检查URL是否有效
    if (!processedUrl || processedUrl.includes('undefined') || processedUrl === url) {
      console.warn(`CDN处理失败，URL无效 [${type}]:`, processedUrl);
      const defaultUrl = sheep.$url.static(DEFAULT_IMAGES[type] || DEFAULT_IMAGES.goods);
      console.log(`使用默认图片 [${type}]:`, defaultUrl);
      return defaultUrl;
    }

    // 确保使用HTTPS协议
    if (processedUrl.startsWith('http:')) {
      processedUrl = processedUrl.replace('http:', 'https:');
      console.log(`转换为HTTPS [${type}]:`, processedUrl);
    }

    // 在微信小程序中，跳过下载预处理，直接使用URL
    // #ifdef MP-WEIXIN
    console.log(`微信小程序环境，直接使用URL [${type}]:`, processedUrl);
    return processedUrl;
    // #endif

    // 其他平台进行图片验证
    const isValid = await validateImageUrl(processedUrl);

    if (isValid) {
      console.log(`图片验证成功 [${type}]:`, processedUrl);
      return processedUrl;
    } else {
      const defaultUrl = sheep.$url.static(DEFAULT_IMAGES[type] || DEFAULT_IMAGES.goods);
      console.warn(`图片加载失败，使用默认图片 [${type}]: ${url} -> ${defaultUrl}`);
      return defaultUrl;
    }
  } catch (error) {
    const defaultUrl = sheep.$url.static(DEFAULT_IMAGES[type] || DEFAULT_IMAGES.goods);
    console.error(`图片验证出错 [${type}]:`, error, `使用默认图片: ${defaultUrl}`);
    return defaultUrl;
  }
}

/**
 * 批量验证图片URL
 * @param {Array} imageList 图片URL数组
 * @returns {Promise<Array>} 验证结果数组
 */
export async function validateImageList(imageList) {
  const results = await Promise.allSettled(
    imageList.map(url => validateImageUrl(url))
  );
  
  return results.map((result, index) => ({
    url: imageList[index],
    isValid: result.status === 'fulfilled' && result.value
  }));
}

export default {
  validateImageUrl,
  getSafeImageUrl,
  validateImageList,
  DEFAULT_IMAGES
};
