/**
 * 图片验证和处理工具
 */
import sheep from '@/sheep';

// 默认图片资源
const DEFAULT_IMAGES = {
  avatar: '/static/images/default-avatar.png',
  goods: '/static/images/default-goods.png',
  background: '/static/images/default-poster-bg.png'
};

/**
 * 验证图片URL是否有效
 * @param {string} url 图片URL
 * @returns {Promise<boolean>} 是否有效
 */
export function validateImageUrl(url) {
  return new Promise((resolve) => {
    if (!url || typeof url !== 'string') {
      resolve(false);
      return;
    }
    
    // 检查是否是有效的URL格式
    if (!url.startsWith('http') && !url.startsWith('/') && !url.startsWith('data:')) {
      resolve(false);
      return;
    }
    
    // 使用 uni.getImageInfo 验证图片
    uni.getImageInfo({
      src: url,
      success: () => resolve(true),
      fail: () => resolve(false)
    });
  });
}

/**
 * 获取安全的图片URL，如果原URL无效则返回默认图片
 * @param {string} url 原始图片URL
 * @param {string} type 图片类型 (avatar, goods, background)
 * @returns {Promise<string>} 安全的图片URL
 */
export async function getSafeImageUrl(url, type = 'goods') {
  try {
    // 如果URL为空，直接返回默认图片
    if (!url) {
      return sheep.$url.static(DEFAULT_IMAGES[type] || DEFAULT_IMAGES.goods);
    }
    
    // 处理CDN URL
    const processedUrl = sheep.$url.cdn(url);
    
    // 验证图片是否可访问
    const isValid = await validateImageUrl(processedUrl);
    
    if (isValid) {
      return processedUrl;
    } else {
      console.warn(`图片加载失败，使用默认图片: ${url}`);
      return sheep.$url.static(DEFAULT_IMAGES[type] || DEFAULT_IMAGES.goods);
    }
  } catch (error) {
    console.error('图片验证出错:', error);
    return sheep.$url.static(DEFAULT_IMAGES[type] || DEFAULT_IMAGES.goods);
  }
}

/**
 * 批量验证图片URL
 * @param {Array} imageList 图片URL数组
 * @returns {Promise<Array>} 验证结果数组
 */
export async function validateImageList(imageList) {
  const results = await Promise.allSettled(
    imageList.map(url => validateImageUrl(url))
  );
  
  return results.map((result, index) => ({
    url: imageList[index],
    isValid: result.status === 'fulfilled' && result.value
  }));
}

export default {
  validateImageUrl,
  getSafeImageUrl,
  validateImageList,
  DEFAULT_IMAGES
};
