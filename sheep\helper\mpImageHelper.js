/**
 * 微信小程序图片处理工具
 */

/**
 * 下载图片到本地临时文件
 * @param {string} url 图片URL
 * @returns {Promise<string>} 本地临时文件路径
 */
export function downloadImageToTemp(url) {
  return new Promise((resolve, reject) => {
    if (!url) {
      reject(new Error('图片URL为空'));
      return;
    }

    console.log('开始下载图片:', url);

    uni.downloadFile({
      url: url,
      success: (res) => {
        if (res.statusCode === 200) {
          console.log('图片下载成功:', res.tempFilePath);
          resolve(res.tempFilePath);
        } else {
          console.error('图片下载失败，状态码:', res.statusCode);
          reject(new Error(`图片下载失败，状态码: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        console.error('图片下载失败:', err);
        reject(new Error(`图片下载失败: ${err.errMsg || '网络错误'}`));
      }
    });
  });
}

/**
 * 预处理图片URL，确保在微信小程序中可用
 * @param {string} url 原始图片URL
 * @returns {Promise<string>} 处理后的图片URL或本地路径
 */
export async function preprocessImageForMP(url) {
  try {
    if (!url) {
      throw new Error('图片URL为空');
    }

    // 如果是本地路径，直接返回
    if (!url.startsWith('http')) {
      return url;
    }

    // 确保使用 HTTPS
    if (url.startsWith('http:')) {
      url = url.replace('http:', 'https:');
    }

    // 在微信小程序中，先下载到本地再使用
    // #ifdef MP-WEIXIN
    try {
      const tempPath = await downloadImageToTemp(url);
      return tempPath;
    } catch (downloadError) {
      console.warn('图片下载失败，尝试直接使用URL:', downloadError);
      return url; // 如果下载失败，仍然尝试使用原URL
    }
    // #endif

    // 其他平台直接返回URL
    return url;
  } catch (error) {
    console.error('图片预处理失败:', error);
    throw error;
  }
}

/**
 * 批量预处理图片
 * @param {Array<string>} urls 图片URL数组
 * @returns {Promise<Array<string>>} 处理后的图片URL数组
 */
export async function preprocessImagesForMP(urls) {
  const results = [];
  
  for (const url of urls) {
    try {
      const processedUrl = await preprocessImageForMP(url);
      results.push(processedUrl);
    } catch (error) {
      console.error(`图片预处理失败 [${url}]:`, error);
      results.push(url); // 失败时使用原URL
    }
  }
  
  return results;
}
